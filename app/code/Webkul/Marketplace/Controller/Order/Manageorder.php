<?php

namespace Webkul\Marketplace\Controller\Order;

// use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Status\HistoryFactory;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultFactory;


class Manageorder extends \Magento\Framework\App\Action\Action

{
    protected $order;
    protected $historyFactory;
    protected $jsonFactory;
    protected $resultPageFactory;

    public function __construct(
        Context $context,
        Order $order,
        HistoryFactory $historyFactory,
        JsonFactory $jsonFactory,
       PageFactory $resultPageFactory
    ) {
       parent::__construct($context);
        $this->order = $order;
        $this->historyFactory = $historyFactory;
        $this->jsonFactory = $jsonFactory;
        $this->resultPageFactory = $resultPageFactory;
    }

    public function execute()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/ordercomment.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In Here");
        $orderId = $this->getRequest()->getParam('order_id');
        $comment = $this->getRequest()->getParam('history_comment');
         $logger->info(print_r($comment,true));

        $newStatus = $this->getRequest()->getParam('custom_order_status');
        $logger->info(print_r($newStatus,true));

        if ($orderId && $newStatus) {
            try {
                $logger->info(print_r($orderId,true));
                $order = $this->order->load($orderId);

                $factory = \Magento\Framework\App\ObjectManager::getInstance()
                    ->get(\Magento\Sales\Model\Order\Status\HistoryFactory::class);
                $repository = \Magento\Framework\App\ObjectManager::getInstance()
                    ->get(\Magento\Sales\Api\OrderStatusHistoryRepositoryInterface::class);

               
                $history = $factory->create();
                $history->setParentId($orderId)
                ->setStatus($newStatus)
                ->setComment($comment)
                ->setIsCustomerNotified(true)
                ->setIsVisibleOnFront(true);

                $order->addStatusHistory($history);

                $order->save();

                $this->messageManager->addSuccessMessage(__('Updated successfully'));


                $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
                $resultRedirect->setUrl($this->_redirect->getRefererUrl());

                return $resultRedirect;
            }catch (\Exception $e) {
              
            }
        }else {
               $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
               $resultRedirect->setUrl($this->_redirect->getRefererUrl());

                return $resultRedirect;
        }
        
    }
}
