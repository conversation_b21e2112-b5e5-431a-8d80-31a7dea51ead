<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<?php
$viewModel = $block->getViewModel();
$orderHelper = $viewModel->getOrderHelper();
$orderId = $block->getRequest()->getParam('id');
?>
<table class="data-table orders wk-mp-list_table" id="my-orders-table">
    <col width="1" />
    <col width="1" />
    <tbody>
        <?php
        foreach ($block->getChildNames() as $key => $blockName) {
            if ($blockName != 'marketplace_order_items') { ?>
                <?= /* @noEscape */ $block->getChildHtml($blockName, false);?>
                <?php
            }
        } ?>
        <?= /* @noEscape */ $block->getChildHtml(); ?>
        <tr>
            <td>
                <input class="carrier input-text"
                    type="text" 
                    name="carrier"
                    placeholder="<?= /* @noEscape */ __('Carrier') ?>"/>
            </td>
        </tr>
        <tr>
            <td>
                <input class="required-entry input-text" type="text"
                    name="tracking_id"
                    placeholder="<?= /* @noEscape */ __('Tracking Number') ?>"/>
            </td>
        </tr>
    </tbody>
</table>
