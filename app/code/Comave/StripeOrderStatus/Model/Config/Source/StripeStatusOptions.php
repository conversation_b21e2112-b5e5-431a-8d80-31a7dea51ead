<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class StripeStatusOptions implements OptionSourceInterface
{
    /**
     * @return array<int, array<string, string>>
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 'Succeeded', 'label' => __('Succeeded')],
            ['value' => 'Pending', 'label' => __('Pending')],
            ['value' => 'Uncaptured', 'label' => __('Uncaptured')],
            ['value' => 'Canceled', 'label' => __('Canceled')],
            ['value' => 'error', 'label' => __('Error')],
            ['value' => 'api_error', 'label' => __('API Error')],
            ['value' => 'not_stripe', 'label' => __('Not Stripe')],
            ['value' => 'no_payment', 'label' => __('No Payment')],
            ['value' => 'no_intent', 'label' => __('No Intent')]
        ];
    }
}
