<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Plugin;

use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;
use Comave\StripeOrderStatus\Helper\StripeGridStatus;

class OrderGridCollectionPlugin
{
    private StripeGridStatus $stripeStatusHelper;
    /** @var array<string, bool> */
    private array $processedCollections = [];

    public function __construct(
        StripeGridStatus $stripeStatusHelper
    ) {
        $this->stripeStatusHelper = $stripeStatusHelper;
    }

    /**
     * Add Stripe payment status to order grid collection after load
     *
     * @param Collection $subject
     * @param Collection $result
     * @return Collection
     */
    public function afterLoad(Collection $subject, Collection $result)
    {
        $collectionId = spl_object_hash($result);
        
        // Prevent infinite loops by checking if we've already processed this collection
        if (isset($this->processedCollections[$collectionId]) || !$result->isLoaded()) {
            return $result;
        }
        
        $this->processedCollections[$collectionId] = true;

        foreach ($result->getItems() as $item) {
            try {
                $stripeStatus = $this->stripeStatusHelper->getStatusForOrderId($item->getData('entity_id'));
                $item->setData('stripe_payment_status', $stripeStatus);
            } catch (\Exception $e) {
                // Log error but don't break the grid
                $item->setData('stripe_payment_status', 'unknown');
            }
        }

        return $result;
    }
}
