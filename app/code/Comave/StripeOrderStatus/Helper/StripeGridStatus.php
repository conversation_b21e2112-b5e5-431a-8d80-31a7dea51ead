<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Helper;

use StripeIntegration\Payments\Model\Stripe\PaymentIntent;
use StripeIntegration\Payments\Helper\Token;
use Magento\Sales\Model\Order;
use Magento\Sales\Api\OrderRepositoryInterface;

class StripeGridStatus
{
    /** @var array<string, string> */
    private array $statusCache = [];

    public function __construct(
        private readonly PaymentIntent $paymentIntent,
        private readonly Token $tokenHelper,
        private readonly OrderRepositoryInterface $orderRepository
    ) {
    }

    public function getStatusForOrder(Order $order): string
    {
        try {
            $payment = $order->getPayment();
            if (!$payment) {
                return 'no_payment';
            }

            // Check if payment method is Stripe
            $paymentMethod = $payment->getMethod();
            
            if (strpos($paymentMethod, 'stripe_payments') !== 0) {
                return 'not_stripe';
            }

            // Try to get transaction ID from various sources
            $transactionId = null;
            
            // First try the last transaction ID
            $lastTransId = $payment->getLastTransId();
            
            if (!empty($lastTransId)) {
                $transactionId = $this->tokenHelper->cleanToken($lastTransId);
            }
            
            // If not found, try additional transaction info
            if (empty($transactionId)) {
                $additionalInformation = $payment->getAdditionalInformation();
                
                if (isset($additionalInformation['payment_intent'])) {
                    $transactionId = $additionalInformation['payment_intent'];
                } elseif (isset($additionalInformation['last_charge_id'])) {
                    $transactionId = $additionalInformation['last_charge_id'];
                } elseif (isset($additionalInformation['checkout_session_id'])) {
                    // For Stripe Checkout sessions, assume successful if we have a session ID
                    // In the future, we could enhance this to retrieve the actual payment intent
                    $sessionId = $additionalInformation['checkout_session_id'];
                    if (!empty($sessionId)) {
                        return 'Succeeded';
                    }
                }
            }
            
            // Clean and validate the transaction ID
            if (!empty($transactionId)) {
                $transactionId = $this->tokenHelper->cleanToken($transactionId);
            }

            if (empty($transactionId)) {
                return 'no_intent';
            }
            
            // Check if it's a payment intent (starts with pi_) or charge (starts with ch_)
            if (strpos($transactionId, "pi_") !== 0 && strpos($transactionId, "ch_") !== 0) {
                return 'no_intent';
            }

            // Check cache first to avoid repeated API calls
            if (isset($this->statusCache[$transactionId])) {
                return $this->statusCache[$transactionId];
            }

            // Limit API calls and add timeout protection
            try {
                $stripeObject = null;
                
                if (strpos($transactionId, "pi_") === 0) {
                    // Handle payment intent
                    try {
                        $paymentIntentModel = $this->paymentIntent->fromPaymentIntentId($transactionId);
                        $stripeObject = $paymentIntentModel->getStripeObject();
                    } catch (\Exception $e) {
                        // If we can't retrieve the payment intent (different account, old data, etc.)
                        // but we have a valid payment intent ID, assume it was successful
                        return 'Succeeded';
                    }
                } elseif (strpos($transactionId, "ch_") === 0) {
                    try {
                        $intent = $this->paymentIntent->fromPaymentIntentId($transactionId, ['status'])->getStripeObject();
                        $stripeObject = $intent;
                    } catch (\Exception $e) {
                        return 'Succeeded'; // Assume successful if we have a charge ID
                    }
                }
                
                if ($stripeObject) {
                    $status = null;

                    if (isset($stripeObject->status)) {
                        $status = $stripeObject->status;
                    } elseif (isset($stripeObject->_values['status'])) {
                        $status = $stripeObject->_values['status'];
                    } elseif (method_exists($stripeObject, 'toArray')) {
                        $array = $stripeObject->toArray();
                        $status = $array['status'] ?? null;
                    }
                    
                    if ($status) {
                        $formattedStatus = $this->formatStatus($status);
                        $this->statusCache[$transactionId] = $formattedStatus;
                        return $formattedStatus;
                    } else {
                        // If we have a stripe object but no status, assume success
                        return 'Succeeded';
                    }
                } else {
                    // If API returns null but we have a valid transaction ID, assume success
                    return 'Succeeded';
                }
            } catch (\Exception $apiException) {
                // Cache the error to avoid repeated failed calls
                $this->statusCache[$transactionId] = 'api_error';
                return 'api_error';
            }

        } catch (\Exception $e) {
            return 'error';
        }
    }

    public function getStatusForOrderId(int|string $orderId): string
    {
        try {
            $order = $this->orderRepository->get((int)$orderId);
            return $this->getStatusForOrder($order);
        } catch (\Exception $e) {
            return 'error';
        }
    }

    private function formatStatus(string $status): string
    {
        // Enhanced status mapping based on official Stripe module logic
        switch ($status) {
            case 'requires_payment_method':
            case 'requires_confirmation':
            case 'requires_action':
            case 'processing':
                return 'Pending';
            case 'requires_capture':
                return 'Uncaptured';
            case 'succeeded':
                return 'Succeeded';
            case 'canceled':
                return 'Canceled';
            case 'pending':
                return 'Pending';
            default:
                // Convert underscores to spaces and capitalize
                return ucwords(str_replace('_', ' ', $status));
        }
    }
}
